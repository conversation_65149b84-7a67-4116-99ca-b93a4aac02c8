<?php
/**
 * Test file for SAP Contract Pricing API
 * This file demonstrates how to test the contract pricing endpoint
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // If not in WordPress, set up basic environment for testing
    define( 'ABSPATH', dirname( __FILE__ ) . '/' );
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>SAP Contract Pricing API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        .endpoint { background: #e7f3ff; padding: 15px; border-left: 4px solid #2196F3; margin: 10px 0; }
        .test-data { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0; }
    </style>
</head>
<body>

<h1>🏷️ SAP Contract Pricing API Test</h1>

<div class="endpoint">
    <h2>📡 API Endpoint Information</h2>
    <p><strong>Endpoint:</strong> <code>/wp-json/wc/v3/sap-contract-pricing</code></p>
    <p><strong>Method:</strong> POST</p>
    <p><strong>Authentication:</strong> WordPress REST API (Basic Auth or Application Passwords)</p>
    <p><strong>Content-Type:</strong> application/json</p>
</div>

<div class="test-data">
    <h2>📋 Test Data Structure</h2>
    <p>The API expects a JSON payload with the following structure:</p>
    <pre>{
  "contractPricing": [
    {
      "materialNumber": "TWSLPL040068",
      "customer_id": "123456",
      "price": {
        "US": {
          "contract_price": 1813.500
        },
        "EU": {
          "contract_price": 111.222
        }
      }
    },
    {
      "materialNumber": "TWSLPL040073",
      "customer_id": "123456",
      "price": {
        "EU": {
          "contract_price": 222.333
        },
        "GB": {
          "contract_price": 333.444
        }
      }
    }
  ]
}</pre>
</div>

<h2>🔧 Field Mapping</h2>
<ul>
    <li><strong>materialNumber</strong> → <code>product_sku</code> (Product SKU in database)</li>
    <li><strong>customer_id</strong> → <code>customer_id</code> (Customer ID in database)</li>
    <li><strong>contract_price</strong> → <code>new_price</code> (Contract price in database)</li>
</ul>

<h2>🗄️ Database Tables</h2>
<p>The API updates the following existing custom tables:</p>
<ul>
    <li><code>wp_contract_pricing</code> (US/USD prices)</li>
    <li><code>wp_contract_pricing_eur</code> (EU/EUR prices)</li>
    <li><code>wp_contract_pricing_gbp</code> (GB/GBP prices)</li>
</ul>

<p>Each table has the structure:</p>
<pre>- customer_id (int, unsigned)
- product_sku (varchar 255)
- new_price (float 10,3)</pre>

<h2>🧪 Testing the API</h2>

<?php
// Test data
$test_data = [
    'contractPricing' => [
        [
            'materialNumber' => 'TWSLPL040068',
            'customer_id' => '123456',
            'price' => [
                'US' => [
                    'contract_price' => 1813.500
                ],
                'EU' => [
                    'contract_price' => 111.222
                ]
            ]
        ],
        [
            'materialNumber' => 'TWSLPL040073',
            'customer_id' => '123456',
            'price' => [
                'EU' => [
                    'contract_price' => 222.333
                ],
                'GB' => [
                    'contract_price' => 333.444
                ]
            ]
        ]
    ]
];

echo "<h3>📤 Test Data (JSON)</h3>\n";
echo "<pre>" . json_encode( $test_data, JSON_PRETTY_PRINT ) . "</pre>\n";

// Check if we're in WordPress environment
if ( function_exists( 'wp_get_current_user' ) ) {
    echo "<h3>🔍 WordPress Environment Check</h3>\n";
    echo "<p class='success'>✅ WordPress functions are available</p>\n";
    
    // Check if the plugin functions exist
    if ( function_exists( 'sap_process_contract_pricing' ) ) {
        echo "<p class='success'>✅ SAP Contract Pricing plugin functions are loaded</p>\n";
    } else {
        echo "<p class='error'>❌ SAP Contract Pricing plugin functions are not loaded. Make sure the plugin is activated.</p>\n";
    }
    
    // Check database tables
    global $wpdb;
    $tables_to_check = [
        $wpdb->prefix . 'contract_pricing',
        $wpdb->prefix . 'contract_pricing_eur',
        $wpdb->prefix . 'contract_pricing_gbp'
    ];
    
    echo "<h3>🗄️ Database Tables Check</h3>\n";
    foreach ( $tables_to_check as $table ) {
        $table_exists = $wpdb->get_var( $wpdb->prepare( "SHOW TABLES LIKE %s", $table ) );
        if ( $table_exists ) {
            $count = $wpdb->get_var( "SELECT COUNT(*) FROM {$table}" );
            echo "<p class='success'>✅ Table <code>{$table}</code> exists with {$count} records</p>\n";
        } else {
            echo "<p class='error'>❌ Table <code>{$table}</code> does not exist</p>\n";
        }
    }
    
} else {
    echo "<h3>⚠️ Standalone Test Mode</h3>\n";
    echo "<p class='info'>This test is running outside WordPress. To test the actual API functionality, run this file from within WordPress.</p>\n";
}
?>

<h2>🚀 cURL Examples</h2>

<h3>Basic Authentication</h3>
<pre>curl -X POST \
  'https://yoursite.com/wp-json/wc/v3/sap-contract-pricing' \
  -H 'Content-Type: application/json' \
  -u 'username:password' \
  -d '{
    "contractPricing": [
      {
        "materialNumber": "TWSLPL040068",
        "customer_id": "123456",
        "price": {
          "US": {
            "contract_price": 1813.500,
            "price_code": "6",
            "valid_to": "31/12/2024"
          }
        }
      }
    ]
  }'</pre>

<h3>Application Password Authentication</h3>
<pre>curl -X POST \
  'https://yoursite.com/wp-json/wc/v3/sap-contract-pricing' \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Basic [base64_encoded_username:app_password]' \
  -d '{
    "contractPricing": [...]
  }'</pre>

<h2>📊 Expected Response</h2>
<pre>{
  "success": true,
  "total_materials_requested": 2,
  "total_materials_processed": 2,
  "total_currencies_updated": 3,
  "processed_materials": [
    {
      "material_number": "TWSLPL040068",
      "customer_id": "123456",
      "product_id": 123,
      "total_currencies_updated": 2,
      "updated_currencies": [...]
    }
  ],
  "errors": [],
  "request_id": "sap_contract_pricing_...",
  "timestamp": "2024-01-01 12:00:00"
}</pre>

<h2>📝 API Logs</h2>
<p>All API calls are logged to: <code>/wp-content/logs/APIlogs.log</code></p>
<p>Logs include:</p>
<ul>
    <li>Request details (method, URL, timestamp, user agent, client IP)</li>
    <li>Authentication information</li>
    <li>Request data (sanitized)</li>
    <li>Processing results for each material and currency</li>
    <li>Response data</li>
    <li>Execution time</li>
</ul>

<h2>🔧 Test Endpoint</h2>
<p>Test if the API is active:</p>
<pre>GET /wp-json/wc/v3/sap-contract-pricing-test</pre>
<p>This endpoint requires no authentication and returns basic status information.</p>

</body>
</html>
