# SAP Contract Pricing API Test Script
# This PowerShell script tests the SAP Contract Pricing API endpoint

param(
    [string]$SiteUrl = "https://yoursite.com",
    [string]$Username = "",
    [string]$Password = "",
    [switch]$UseApplicationPassword = $false,
    [switch]$TestEndpoint = $false
)

# Colors for output
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Cyan = "Cyan"
$White = "White"

Write-Host "🏷️ SAP Contract Pricing API Test Script" -ForegroundColor $Cyan
Write-Host "=========================================" -ForegroundColor $Cyan
Write-Host ""

# Check if credentials are provided
if ([string]::IsNullOrEmpty($Username) -or [string]::IsNullOrEmpty($Password)) {
    Write-Host "❌ ERROR: Username and Password are required!" -ForegroundColor $Red
    Write-Host ""
    Write-Host "Usage Examples:" -ForegroundColor $Yellow
    Write-Host "  # Basic Authentication:" -ForegroundColor $White
    Write-Host "  .\test-sap-contract-pricing.ps1 -SiteUrl 'https://yoursite.com' -Username 'your_username' -Password 'your_password'" -ForegroundColor $White
    Write-Host ""
    Write-Host "  # Application Password:" -ForegroundColor $White
    Write-Host "  .\test-sap-contract-pricing.ps1 -SiteUrl 'https://yoursite.com' -Username 'your_username' -Password 'xxxx xxxx xxxx xxxx' -UseApplicationPassword" -ForegroundColor $White
    Write-Host ""
    Write-Host "  # Test endpoint only:" -ForegroundColor $White
    Write-Host "  .\test-sap-contract-pricing.ps1 -SiteUrl 'https://yoursite.com' -TestEndpoint" -ForegroundColor $White
    exit 1
}

# API endpoints
$TestApiEndpoint = "$SiteUrl/wp-json/wc/v3/sap-contract-pricing-test"
$ApiEndpoint = "$SiteUrl/wp-json/wc/v3/sap-contract-pricing"

# Test the basic endpoint first (no auth required)
Write-Host "🔍 Testing API availability..." -ForegroundColor $Yellow

try {
    $testResponse = Invoke-RestMethod -Uri $TestApiEndpoint -Method Get -TimeoutSec 30
    
    Write-Host "✅ API Endpoint is active!" -ForegroundColor $Green
    Write-Host "  Status: $($testResponse.status)" -ForegroundColor $White
    Write-Host "  Message: $($testResponse.message)" -ForegroundColor $White
    Write-Host "  Timestamp: $($testResponse.timestamp)" -ForegroundColor $White
    Write-Host ""
} catch {
    Write-Host "❌ ERROR: Could not reach the API endpoint!" -ForegroundColor $Red
    Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor $Red
    Write-Host "  Make sure the plugin is activated and the site URL is correct." -ForegroundColor $Yellow
    exit 1
}

# If only testing endpoint, exit here
if ($TestEndpoint) {
    Write-Host "✅ Endpoint test completed successfully!" -ForegroundColor $Green
    exit 0
}

# Prepare authentication
$authType = if ($UseApplicationPassword) { "Application Password" } else { "Basic Authentication" }
Write-Host "🔐 Using $authType" -ForegroundColor $Yellow

# Create credentials
$securePassword = ConvertTo-SecureString $Password -AsPlainText -Force
$credential = New-Object System.Management.Automation.PSCredential($Username, $securePassword)

# Test data matching the expected JSON structure
$testData = @{
    contractPricing = @(
        @{
            materialNumber = "TWSLPL040068"
            customer_id = "123456"
            price = @{
                US = @{
                    contract_price = 1813.500
                }
                EU = @{
                    contract_price = 111.222
                }
            }
        },
        @{
            materialNumber = "TWSLPL040073"
            customer_id = "123456"
            price = @{
                EU = @{
                    contract_price = 222.333
                }
                GB = @{
                    contract_price = 333.444
                }
            }
        }
    )
} | ConvertTo-Json -Depth 10

Write-Host "📤 Sending test data to API..." -ForegroundColor $Yellow
Write-Host "  Endpoint: $ApiEndpoint" -ForegroundColor $White
Write-Host "  Materials: 2" -ForegroundColor $White
Write-Host "  Customer ID: 123456" -ForegroundColor $White
Write-Host ""

# Prepare headers
$headers = @{
    'Content-Type' = 'application/json'
}

Write-Host "📋 Test Data Structure:" -ForegroundColor $Yellow
Write-Host $testData -ForegroundColor $White
Write-Host ""

try {
    $response = Invoke-RestMethod -Uri $ApiEndpoint -Method Post -Body $testData -Headers $headers -Credential $credential -TimeoutSec 60
    
    Write-Host "✅ SUCCESS: API processed the request!" -ForegroundColor $Green
    Write-Host ""
    
    Write-Host "📊 Response Details:" -ForegroundColor $Yellow
    Write-Host "  Success: $($response.success)" -ForegroundColor $White
    Write-Host "  Materials Requested: $($response.total_materials_requested)" -ForegroundColor $White
    Write-Host "  Materials Processed: $($response.total_materials_processed)" -ForegroundColor $White
    Write-Host "  Total Currencies Updated: $($response.total_currencies_updated)" -ForegroundColor $White
    Write-Host "  Request ID: $($response.request_id)" -ForegroundColor $White
    Write-Host "  Timestamp: $($response.timestamp)" -ForegroundColor $White
    Write-Host ""
    
    if ($response.processed_materials) {
        Write-Host "📦 Processed Materials:" -ForegroundColor $Yellow
        foreach ($material in $response.processed_materials) {
            Write-Host "  - Material: $($material.material_number)" -ForegroundColor $White
            Write-Host "    Customer ID: $($material.customer_id)" -ForegroundColor $White
            Write-Host "    Product ID: $($material.product_id)" -ForegroundColor $White
            Write-Host "    Currencies Updated: $($material.total_currencies_updated)" -ForegroundColor $White
            
            if ($material.updated_currencies) {
                Write-Host "    Updated Currencies:" -ForegroundColor $White
                foreach ($currency in $material.updated_currencies) {
                    Write-Host "      - $($currency.currency): $($currency.price) (Code: $($currency.price_code), Valid: $($currency.valid_to))" -ForegroundColor $White
                }
            }
            Write-Host ""
        }
    }
    
    if ($response.errors -and $response.errors.Count -gt 0) {
        Write-Host "⚠️ Errors encountered:" -ForegroundColor $Yellow
        foreach ($error in $response.errors) {
            Write-Host "  - $error" -ForegroundColor $Red
        }
        Write-Host ""
    }
    
    Write-Host "✅ Contract pricing data has been successfully processed!" -ForegroundColor $Green
    Write-Host ""
    Write-Host "📝 Check the API logs at: /wp-content/logs/APIlogs.log" -ForegroundColor $Cyan
    
} catch {
    Write-Host "❌ ERROR: API request failed!" -ForegroundColor $Red
    Write-Host ""
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode.value__
        $statusDescription = $_.Exception.Response.StatusDescription
        
        Write-Host "  HTTP Status: $statusCode $statusDescription" -ForegroundColor $Red
        
        # Try to get the response content for more details
        try {
            $responseStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($responseStream)
            $responseContent = $reader.ReadToEnd()
            $reader.Close()
            
            if ($responseContent) {
                Write-Host "  Response: $responseContent" -ForegroundColor $Red
            }
        } catch {
            # Ignore errors when trying to read response content
        }
        
        # Provide specific guidance based on status code
        switch ($statusCode) {
            401 { 
                Write-Host "  🔐 Authentication failed. Check your username and password." -ForegroundColor $Yellow
                if ($UseApplicationPassword) {
                    Write-Host "  💡 Make sure you're using an Application Password, not your regular password." -ForegroundColor $Yellow
                }
            }
            403 { 
                Write-Host "  🚫 Access forbidden. Your user account may not have sufficient permissions." -ForegroundColor $Yellow
                Write-Host "  💡 Required capabilities: manage_options, manage_woocommerce, edit_products, etc." -ForegroundColor $Yellow
            }
            404 { 
                Write-Host "  🔍 Endpoint not found. Make sure the SAP Contract Pricing plugin is activated." -ForegroundColor $Yellow
            }
            500 { 
                Write-Host "  💥 Server error. Check the WordPress error logs for details." -ForegroundColor $Yellow
            }
        }
    } else {
        Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor $Red
    }
    
    Write-Host ""
    Write-Host "🔧 Troubleshooting Tips:" -ForegroundColor $Cyan
    Write-Host "  1. Verify the site URL is correct" -ForegroundColor $White
    Write-Host "  2. Check that the SAP Contract Pricing plugin is activated" -ForegroundColor $White
    Write-Host "  3. Verify your authentication credentials" -ForegroundColor $White
    Write-Host "  4. Check WordPress error logs" -ForegroundColor $White
    Write-Host "  5. Review API logs at /wp-content/logs/APIlogs.log" -ForegroundColor $White
}

Write-Host ""
Write-Host "🏁 Test completed!" -ForegroundColor $Cyan
